namespace OutlookCleaner;

using Azure.Core;
using Azure.Identity;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Text.Json;

internal class Program
{
    private static CleanerConfiguration cleanerConfiguration = new();
    private static GraphServiceClient? graphClient;

    private const string TokenName = $"{nameof(OutlookCleaner)}.tokencache";

    private static string TokenFilePath => Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
        nameof(OutlookCleaner),
        TokenName);

    public static async Task Main(string[] args)
    {
        Directory.CreateDirectory(Path.GetDirectoryName(TokenFilePath)!);
        cleanerConfiguration = GetCleanerConfiguration();

        // Define your Azure app settings
        var clientId = "3455edb7-4472-4ab4-bc9e-ce1269e8db52";
        var tenantId = "consumers"; // use "consumers" for personal accounts
        string[] scopes = ["https://graph.microsoft.com/Mail.ReadWrite", "https://graph.microsoft.com/MailboxSettings.Read"];

        var deviceCodeCredentialOptions = new DeviceCodeCredentialOptions()
        {
            ClientId = clientId,
            TenantId = tenantId,
            DeviceCodeCallback = (info, cancel) =>
            {
                // Display the device code message to the user. This tells them where to go to sign in and provides the code to use.
                Console.WriteLine(info.Message);
                return Task.FromResult(0);
            },
            TokenCachePersistenceOptions = new TokenCachePersistenceOptions() { Name = TokenName }
        };
        DeviceCodeCredential deviceCodeCredential;
        if (File.Exists(TokenFilePath))
        {
            using var fileStream = new FileStream(TokenFilePath, FileMode.Open, FileAccess.Read);
            deviceCodeCredentialOptions.AuthenticationRecord = await AuthenticationRecord.DeserializeAsync(fileStream).ConfigureAwait(true);
            deviceCodeCredential = new DeviceCodeCredential(deviceCodeCredentialOptions);
        }
        else
        {
            deviceCodeCredential = new DeviceCodeCredential(deviceCodeCredentialOptions);
            var authenticationRecord = await deviceCodeCredential.AuthenticateAsync(new TokenRequestContext(scopes)).ConfigureAwait(true);
            //
            using var fileStream1 = new FileStream(TokenFilePath, FileMode.Create, FileAccess.Write);
            await authenticationRecord.SerializeAsync(fileStream1).ConfigureAwait(true);
        }

        graphClient = new GraphServiceClient(deviceCodeCredential, scopes);

        var inboxFolder = await GetMailFolder("Inbox");
        var junkFolder = await GetMailFolder("Junk Email");
        var deletedItemsFolder = await GetMailFolder("Deleted Items");
        if (inboxFolder?.Id is null || junkFolder?.Id is null || deletedItemsFolder?.Id is null)
        {
            return;
        }
        
        // Get blocked senders information
        await GetBlockedSendersInfo();

        await DeleteMessagesFromFolder(junkFolder.Id, deletedItemsFolder.Id);
        await DeleteMessagesFromFolder(inboxFolder.Id, deletedItemsFolder.Id);
    }

    private static async Task<MailFolder?> GetMailFolder(string displayName)
    {
        var response = await graphClient!.Me.MailFolders
            .GetAsync(config =>
            {
                config.QueryParameters.Filter = $"displayName eq '{displayName}'";
            });

        var folder = response?.Value?.SingleOrDefault();
        return folder;
    }

    private static async Task GetBlockedSendersInfo()
    {
        try
        {
            Console.WriteLine("=== Junk Email Settings ===");

            // Get mailbox settings - this doesn't include blocked senders list directly
            // but provides other junk email related settings
            var mailboxSettings = await graphClient!.Me.MailboxSettings.GetAsync();

            if (mailboxSettings != null)
            {
                Console.WriteLine($"Time Zone: {mailboxSettings.TimeZone}");
                Console.WriteLine($"Language: {mailboxSettings.Language?.DisplayName} ({mailboxSettings.Language?.Locale})");

                if (mailboxSettings.AutomaticRepliesSetting != null)
                {
                    Console.WriteLine($"Automatic Replies Status: {mailboxSettings.AutomaticRepliesSetting.Status}");
                }
            }

            // Note: Microsoft Graph API doesn't provide direct access to blocked senders list
            // The blocked senders list is managed internally by Exchange Online
            // We can only add to it using the markAsJunk API, but cannot retrieve the existing list

            Console.WriteLine("\nNote: Microsoft Graph API doesn't provide direct access to the blocked senders list.");
            Console.WriteLine("The blocked senders are managed internally by Exchange Online.");
            Console.WriteLine("You can add senders to the blocked list using the markAsJunk API when processing messages.");

            // Alternative: Analyze junk folder to identify frequently blocked senders
            await AnalyzeJunkFolderSenders();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting blocked senders info: {ex.Message}");
        }
    }

    private static async Task AnalyzeJunkFolderSenders()
    {
        try
        {
            Console.WriteLine("\n=== Analyzing Junk Folder for Common Senders ===");

            var junkFolder = await GetMailFolder("Junk Email");
            if (junkFolder?.Id == null)
            {
                Console.WriteLine("Junk Email folder not found.");
                return;
            }

            // Get messages from junk folder (last 30 days)
            DateTime startDate = DateTime.Now.AddDays(-30);
            var response = await graphClient!.Me.MailFolders[junkFolder.Id].Messages
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = $"receivedDateTime ge {startDate:yyyy-MM-ddTHH:mm:ssZ}";
                    requestConfiguration.QueryParameters.Top = 500; // Limit to avoid too much data
                    requestConfiguration.QueryParameters.Select = ["from", "sender", "subject", "receivedDateTime"];
                });

            var messages = response?.Value?.ToList() ?? [];

            if (!messages.Any())
            {
                Console.WriteLine("No messages found in Junk Email folder from the last 30 days.");
                return;
            }

            // Group by sender to find most frequent junk senders
            var senderCounts = messages
                .Where(m => m.From?.EmailAddress?.Address != null)
                .GroupBy(m => m.From!.EmailAddress!.Address!.ToLowerInvariant())
                .Select(g => new { Email = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(10)
                .ToList();

            Console.WriteLine($"\nTop 10 most frequent senders in Junk folder (last 30 days):");
            foreach (var sender in senderCounts)
            {
                Console.WriteLine($"  {sender.Email}: {sender.Count} messages");
            }

            // Group by domain to find most frequent junk domains
            var domainCounts = messages
                .Where(m => m.From?.EmailAddress?.Address != null)
                .Select(m => m.From!.EmailAddress!.Address!)
                .Where(email => email.Contains('@'))
                .Select(email => email.Split('@')[1].ToLowerInvariant())
                .GroupBy(domain => domain)
                .Select(g => new { Domain = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(10)
                .ToList();

            Console.WriteLine($"\nTop 10 most frequent domains in Junk folder (last 30 days):");
            foreach (var domain in domainCounts)
            {
                Console.WriteLine($"  {domain.Domain}: {domain.Count} messages");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error analyzing junk folder: {ex.Message}");
        }
    }

    private static async Task DeleteMessagesFromFolder(string folderId, string deletedItemsFolderId)
    {
        DateTime startDate = DateTime.Now.AddDays(-30);

        var response = await graphClient!.Me.MailFolders[folderId].Messages
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter = $"receivedDateTime ge {startDate:yyyy-MM-ddTHH:mm:ssZ}";
                requestConfiguration.QueryParameters.Orderby = ["receivedDateTime desc"];
                requestConfiguration.QueryParameters.Top = 1000; // Adjust the number of messages as needed
            });

        var messages = response?.Value?.ToList() ?? [];

        foreach (var message in messages)
        {
            if (string.IsNullOrWhiteSpace(message.Id)) continue;

            string fromAddress = message.From?.EmailAddress?.Address ?? string.Empty;
            string senderAddress = message.Sender?.EmailAddress?.Address ?? string.Empty;
            
            if (cleanerConfiguration.SpamDomainSuffixes.Any(suffix => 
                fromAddress.EndsWith(suffix, StringComparison.OrdinalIgnoreCase)
                || senderAddress.EndsWith(suffix, StringComparison.OrdinalIgnoreCase)))
            {
                await MoveMessage(message.Id, deletedItemsFolderId);
                continue;
            }

            var allRecipients = new Recipient[0]
                .Union(message.ToRecipients ?? [])
                .Union(message.CcRecipients ?? [])
                .Union(message.BccRecipients ?? [])
                .ToArray();

            if (!allRecipients.Any())
            {
                await MoveMessage(message.Id, deletedItemsFolderId);
            }
        }
    }

    private static CleanerConfiguration GetCleanerConfiguration()
    {
        var folders = AppDomain.CurrentDomain.BaseDirectory.Split(Path.DirectorySeparatorChar, StringSplitOptions.None).ToList();
        var index = folders.IndexOf(AppDomain.CurrentDomain.FriendlyName);
        if (index < 0)
        {
            return new();
        }

        folders.RemoveRange(index + 1, folders.Count - index - 1);
        var projectDirectory = Path.Combine(folders.ToArray());

        string filePath = Path.Combine(projectDirectory, "configuration.json");
        string json = File.ReadAllText(filePath);
        var cleanerConfiguration = JsonSerializer.Deserialize<CleanerConfiguration>(json);
        return cleanerConfiguration ?? new();
    }

    private static async Task MoveMessage(string messageId, string destinationFolderId)
    {
        await graphClient!.Me.Messages[messageId]
            .Move
            .PostAsync(new()
            {
                DestinationId = destinationFolderId
            });
    }

    /// <summary>
    /// Marks a message as junk, which adds the sender to the blocked senders list
    /// and optionally moves the message to the Junk Email folder
    /// </summary>
    /// <param name="messageId">The ID of the message to mark as junk</param>
    /// <param name="moveToJunk">Whether to move the message to Junk Email folder</param>
    private static async Task MarkMessageAsJunk(string messageId, bool moveToJunk = true)
    {
        try
        {
            // Note: This API is in beta, so it might not be available in the stable SDK
            // You may need to use the beta SDK or make direct HTTP calls
            Console.WriteLine($"Marking message {messageId} as junk (moveToJunk: {moveToJunk})");

            // This would be the call if using the beta SDK:
            // await graphClient!.Me.Messages[messageId].MarkAsJunk.PostAsync(new() { MoveToJunk = moveToJunk });

            // For now, we'll just move to junk folder manually
            if (moveToJunk)
            {
                var junkFolder = await GetMailFolder("Junk Email");
                if (junkFolder?.Id != null)
                {
                    await MoveMessage(messageId, junkFolder.Id);
                    Console.WriteLine($"Message moved to Junk Email folder. Note: Sender was not automatically added to blocked list (requires beta API).");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error marking message as junk: {ex.Message}");
        }
    }
}